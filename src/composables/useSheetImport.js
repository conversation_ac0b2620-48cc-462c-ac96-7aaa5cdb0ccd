import { useStates } from '@/store/states'
import * as XLSX from 'xlsx'
import Papa from 'papaparse'

/**
 * Export sheet data to CSV format
 * @param {Object} sheetData - Sheet data object with columns and rows
 * @param {string} sheetName - Name of the sheet for filename
 * @returns {Promise<void>}
 */
export async function exportSheetToCSV(sheetData, sheetName) {
  if (!sheetData || !sheetData.columns || !sheetData.rows) {
    throw new Error('Invalid sheet data')
  }

  try {
    // Prepare data for CSV export
    const csvData = sheetData.rows.map(row => {
      const csvRow = {}
      sheetData.columns.forEach(col => {
        csvRow[col.title || col.field] = row[col.field] || ''
      })
      return csvRow
    })

    // Convert to CSV using Papa Parse
    const csv = Papa.unparse(csvData)

    // Create blob and download
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `${sheetName || 'sheet'}_${timestamp}.csv`

    // Create download link and trigger download
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()

    // Cleanup
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    return filename
  } catch (error) {
    console.error('Export failed:', error)
    throw new Error(`导出失败: ${error.message}`)
  }
}

/**
 * Export sheet data to Excel format
 * @param {Object} sheetData - Sheet data object with columns and rows
 * @param {string} sheetName - Name of the sheet for filename
 * @returns {Promise<void>}
 */
export async function exportSheetToExcel(sheetData, sheetName) {
  if (!sheetData || !sheetData.columns || !sheetData.rows) {
    throw new Error('Invalid sheet data')
  }

  try {
    // Create workbook and worksheet
    const wb = XLSX.utils.book_new()

    // Prepare data for Excel export
    const wsData = [
      // Header row
      sheetData.columns.map(col => col.title || col.field),
      // Data rows
      ...sheetData.rows.map(row =>
        sheetData.columns.map(col => row[col.field] || '')
      )
    ]

    const ws = XLSX.utils.aoa_to_sheet(wsData)
    XLSX.utils.book_append_sheet(wb, ws, sheetName || 'Sheet1')

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const filename = `${sheetName || 'sheet'}_${timestamp}.xlsx`

    // Write and download
    XLSX.writeFile(wb, filename)

    return filename
  } catch (error) {
    console.error('Export failed:', error)
    throw new Error(`导出失败: ${error.message}`)
  }
}

export async function importAndAddSheet(file) {
  const statesStore = useStates()
  const tableState = statesStore.states.find((s) => s.componentName === 'table')
  if (!tableState) return

  let columns = []
  let rows = []
  if (file.name.endsWith('.csv')) {
    // Parse CSV
    const text = await file.text()
    const result = Papa.parse(text, { header: true })
    columns = result.meta.fields.map((field) => ({ field, title: field }))
    rows = result.data.filter((row) => Object.values(row).some((v) => v !== undefined && v !== ''))
  } else {
    // Parse Excel
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data, { type: 'array' })
    const wsname = workbook.SheetNames[0]
    const ws = workbook.Sheets[wsname]
    const json = XLSX.utils.sheet_to_json(ws, { header: 1 })
    if (json.length > 0) {
      columns = json[0].map((field) => ({ field, title: field }))
      rows = json.slice(1).map((row) => {
        const obj = {}
        columns.forEach((col, i) => {
          obj[col.field] = row[i]
        })
        return obj
      })
    }
  }
  if (!columns.length || !rows.length) return
  const idx = Object.keys(tableState.sheetData || {}).length + 1
  const key = `sheet${Date.now()}`
  const newSheetData = { ...(tableState.sheetData || {}) }
  newSheetData[key] = {
    name: file.name.replace(/\.[^.]+$/, ''),
    columns,
    rows,
  }
  statesStore.updateComponent(tableState.componentId, {
    ...tableState,
    sheetData: newSheetData,
    activeSheet: key,
  })
}
